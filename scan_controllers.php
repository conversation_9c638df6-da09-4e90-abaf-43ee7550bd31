<?php

/**
 * 扫描控制器文件，检查public function方法中是否包含error_context字符串
 */

$controllerDir = 'php/api/app/Http/Controllers/Api';
$results = [];

// 获取所有控制器文件
$files = glob($controllerDir . '/*.php');

echo "开始扫描 " . count($files) . " 个控制器文件...\n\n";

foreach ($files as $file) {
    $filename = basename($file);
    echo "正在扫描: $filename\n";
    
    $content = file_get_contents($file);
    if ($content === false) {
        echo "  错误: 无法读取文件 $filename\n";
        continue;
    }
    
    // 使用正则表达式匹配所有public function方法
    $pattern = '/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}/s';
    preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);
    
    $methodsWithoutErrorContext = [];
    
    foreach ($matches as $match) {
        $methodName = $match[1];
        $methodBody = $match[0];
        
        // 跳过构造函数
        if ($methodName === '__construct') {
            continue;
        }
        
        // 检查方法体中是否包含error_context
        if (strpos($methodBody, 'error_context') === false) {
            $methodsWithoutErrorContext[] = $methodName;
        }
    }
    
    if (!empty($methodsWithoutErrorContext)) {
        $results[] = [
            'file' => $filename,
            'methods' => $methodsWithoutErrorContext
        ];
    }
}

// 输出结果
echo "\n" . str_repeat("=", 80) . "\n";
echo "扫描结果报告\n";
echo str_repeat("=", 80) . "\n\n";

if (empty($results)) {
    echo "所有控制器的public function方法都包含error_context字符串。\n";
} else {
    $counter = 1;
    foreach ($results as $result) {
        $methodCount = count($result['methods']);
        echo "### {$counter}. {$result['file']} ({$methodCount}个方法)\n";
        
        foreach ($result['methods'] as $method) {
            echo "- [ ] `{$method}()`\n";
        }
        echo "\n";
        $counter++;
    }
}

echo "扫描完成！\n";
